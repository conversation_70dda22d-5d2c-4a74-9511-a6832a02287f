/**
 * Filin Base - Browser-only exports
 * 
 * Экспортирует только те компоненты, которые нужны в браузере.
 * Исключает серверные компоненты типа otlp-collector, которые используют Node.js модули.
 */

// Экспорт трассировки
export * from './trace';
export { TraceConfigManager } from './trace/TraceConfig';
export { IdGenerator, setIdGenerator, getIdGenerator, FallbackIdGenerator, initializeFallbackGenerator } from './trace/IdGenerator';

// Экспорт W3C Trace Context компонентов (избегаем дублирования TraceContext)
export { W3CTraceContext, W3CTraceState, W3CBaggage, ContextPropagation } from './w3c';
export type { SpanContext } from './w3c';

// Экспорт схемы и валидации
export * from './schema';
export { FILIN_SCHEMA_ID, FILIN_SCHEMA_VERSION, SCHEMA_CONFIG } from './schema/SchemaConstants';

// Экспорт только браузерно-совместимых транспортов (избегаем дублирования TraceEvent)
export {
    TraceTransport,
    TransportConfig,
    OTLPConfig,
    HTTPConfig,
    FileConfig,
    ConsoleConfig,
    BatchConfig,
    SamplingConfig
} from './transports/types';
export { TransportManager, TransportManagerConfig } from './transports/manager';
export * from './transports/otlp';
export * from './transports/otlp-base';
