{"name": "filin-base", "version": "1.0.0", "description": "Базовые компоненты и утилиты для экосистемы Filin", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "./trace": {"types": "./dist/trace/index.d.ts", "default": "./dist/trace/index.js"}, "./browser/trace": {"types": "./dist/browser/trace/index.d.ts", "default": "./dist/browser/trace/index.js"}, "./trace/*": {"types": "./dist/trace/*.d.ts", "default": "./dist/trace/*.js"}, "./w3c": {"types": "./dist/w3c/index.d.ts", "default": "./dist/w3c/index.js"}, "./schema": {"types": "./dist/schema/index.d.ts", "default": "./dist/schema/index.js"}, "./transports": {"types": "./dist/transports/index.d.ts", "default": "./dist/transports/index.js"}, "./browser/transports": {"types": "./dist/browser/transports/index.d.ts", "default": "./dist/browser/transports/index.js"}}, "files": ["dist/**/*", "schemas/**/*"], "scripts": {"build": "tsc", "postbuild": "mkdir -p dist/schemas && cp -r schemas/* dist/schemas/", "watch": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint --ext .ts ./src", "lint:fix": "eslint --fix --ext .ts ./src", "test": "cd ../filin-tests && npm test -- --testPathPattern=filin-base"}, "keywords": ["filin", "base", "trace", "utilities"], "author": "Filin Team", "license": "Apache-2.0", "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1"}, "devDependencies": {"@types/node": "^24.3.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0"}}