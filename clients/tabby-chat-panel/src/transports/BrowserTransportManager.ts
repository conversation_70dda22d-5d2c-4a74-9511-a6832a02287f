/**
 * Browser Transport Manager для tabby-chat-panel
 * 
 * Управляет множественными транспортами в браузерном окружении с поддержкой
 * автоматического fallback и батчирования.
 */

import { TraceEvent, TraceTransport, ConsoleConfig, OTLPConfig } from 'filin-base/browser';
import { TransportManager } from 'filin-base';
import { LocalStorageTransport, LocalStorageConfig } from './LocalStorageTransport';
import { FetchTransport, FetchTransportConfig } from './FetchTransport';
import { ConsoleTransport } from './ConsoleTransport';
import { OTLPTransport, OTLPTransportConfig } from './OTLPTransport';
import { TraceConfigBrowser } from '../trace/TraceConfigBrowser';

export interface BrowserTransportManagerConfig {
  localStorage?: LocalStorageConfig;
  fetch?: FetchTransportConfig;
  console?: ConsoleConfig;
  otlp?: OTLPTransportConfig;
  defaultTransports?: string[];
  batchConfig?: {
    size: number;
    timeout: number;
    maxRetries: number;
    retryDelay: number;
  };
}

export class BrowserTransportManager implements TransportManager {
  private transports = new Map<string, TraceTransport>();
  private config: BrowserTransportManagerConfig;

  constructor(config: BrowserTransportManagerConfig = {}) {
    this.config = config;
    this.initializeDefaultTransports();
  }

  addTransport(name: string, transport: TraceTransport): void {
    this.transports.set(name, transport);
  }

  removeTransport(name: string): void {
    const transport = this.transports.get(name);
    if (transport) {
      transport.close().catch(error => {
        console.warn(`Failed to close transport ${name}:`, error);
      });
      this.transports.delete(name);
    }
  }

  async send(event: TraceEvent): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [name, transport] of Array.from(this.transports)) {
      if (transport.enabled) {
        promises.push(
          transport.send(event).catch(error => {
            console.warn(`Transport ${name} failed to send event:`, error);
            // Не прерываем выполнение других транспортов
          })
        );
      }
    }

    // Ждем завершения всех транспортов (даже с ошибками)
    await Promise.allSettled(promises);
  }

  async sendBatch(events: TraceEvent[]): Promise<void> {
    if (events.length === 0) return;

    const promises: Promise<void>[] = [];

    for (const [name, transport] of Array.from(this.transports)) {
      if (transport.enabled) {
        promises.push(
          transport.sendBatch(events).catch(error => {
            console.warn(`Transport ${name} failed to send batch:`, error);
            // Не прерываем выполнение других транспортов
          })
        );
      }
    }

    // Ждем завершения всех транспортов (даже с ошибками)
    await Promise.allSettled(promises);
  }

  async close(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [name, transport] of Array.from(this.transports)) {
      promises.push(
        transport.close().catch(error => {
          console.warn(`Failed to close transport ${name}:`, error);
        })
      );
    }

    await Promise.allSettled(promises);
    this.transports.clear();
  }

  /**
   * Инициализирует транспорты по умолчанию
   */
  private initializeDefaultTransports(): void {
    // Console transport (всегда доступен)
    if (this.config.console?.enabled !== false) {
      const consoleTransport = new ConsoleTransport({
        enabled: true,
        format: this.config.console?.format || 'json',
        colorize: this.config.console?.colorize !== false
      });
      this.addTransport('console', consoleTransport);
    }

    // LocalStorage transport
    if (this.config.localStorage && this.config.localStorage.enabled && LocalStorageTransport.isAvailable()) {
      const localStorageTransport = new LocalStorageTransport({
        maxEvents: 1000,
        storageKey: 'filin-trace-events',
        ...this.config.localStorage
      });
      this.addTransport('localStorage', localStorageTransport);
    }

    // Fetch transport
    if (this.config.fetch && this.config.fetch.enabled && FetchTransport.isAvailable()) {
      const fetchTransport = new FetchTransport({
        batchSize: 50,
        batchTimeout: 10000,
        maxRetries: 3,
        retryDelay: 1000,
        ...this.config.fetch
      });
      this.addTransport('fetch', fetchTransport);
    }

    // OTLP transport
    if (this.config.otlp && this.config.otlp.enabled && OTLPTransport.isAvailable()) {
      const { protocol, ...otlpConfig } = this.config.otlp;
      const otlpTransport = new OTLPTransport({
        batchSize: 50,
        batchTimeout: 10000,
        maxRetries: 3,
        retryDelay: 1000,
        protocol: protocol || 'http',
        ...otlpConfig
      });
      this.addTransport('otlp', otlpTransport);
    }
  }

  /**
   * Получает список активных транспортов
   */
  getActiveTransports(): string[] {
    return Array.from(this.transports.entries())
      .filter(([, transport]) => transport.enabled)
      .map(([name]) => name);
  }

  /**
   * Получает транспорт по имени
   */
  getTransport(name: string): TraceTransport | undefined {
    return this.transports.get(name);
  }

  /**
   * Принудительная отправка всех накопленных данных
   */
  async flush(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [name, transport] of Array.from(this.transports)) {
      if (transport.enabled && 'flush' in transport) {
        promises.push(
          (transport as any).flush().catch((error: any) => {
            console.warn(`Failed to flush transport ${name}:`, error);
          })
        );
      }
    }

    await Promise.allSettled(promises);
  }

  /**
   * Получает статистику всех транспортов
   */
  getStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const [name, transport] of Array.from(this.transports)) {
      stats[name] = {
        enabled: transport.enabled,
        name: transport.name
      };

      // Добавляем специфичную статистику если доступна
      if ('getStorageStats' in transport) {
        stats[name].storage = (transport as any).getStorageStats();
      }

      if ('getBatchStats' in transport) {
        stats[name].batch = (transport as any).getBatchStats();
      }
    }

    return stats;
  }

  /**
   * Создает конфигурацию по умолчанию для браузера
   */
  static createDefaultConfig(): BrowserTransportManagerConfig {
    return TraceConfigBrowser.createTransportConfig();
  }

  /**
   * Создает конфигурацию для интеграции с Jaeger
   */
  static createJaegerConfig(jaegerEndpoint: string): BrowserTransportManagerConfig {
    const traceConfig = TraceConfigBrowser.loadDefaultConfig();
    // Обновляем конфигурацию для Jaeger
    if (traceConfig.jaeger) {
      traceConfig.jaeger.uiUrl = jaegerEndpoint;
    }
    if (traceConfig.otlp) {
      traceConfig.otlp.endpoint = jaegerEndpoint.replace(':16686', ':4318');
    }

    return TraceConfigBrowser.createTransportConfig(traceConfig);
  }

  /**
   * Создает конфигурацию для интеграции с Grafana
   */
  static createGrafanaConfig(grafanaEndpoint: string, apiKey?: string): BrowserTransportManagerConfig {
    const traceConfig = TraceConfigBrowser.loadDefaultConfig();
    // Обновляем конфигурацию для Grafana
    if (traceConfig.otlp) {
      traceConfig.otlp.endpoint = grafanaEndpoint;
      if (apiKey) {
        traceConfig.otlp.headers = { ...traceConfig.otlp.headers, 'Authorization': `Bearer ${apiKey}` };
      }
    }

    const transportConfig = TraceConfigBrowser.createTransportConfig(traceConfig);
    // Увеличиваем лимиты для Grafana
    if (transportConfig.localStorage) {
      transportConfig.localStorage.maxEvents = 2000;
    }
    if (transportConfig.batchConfig) {
      transportConfig.batchConfig.size = 100;
      transportConfig.batchConfig.timeout = 5000;
      transportConfig.batchConfig.maxRetries = 5;
      transportConfig.batchConfig.retryDelay = 2000;
    }

    return transportConfig;
  }

  /**
   * Создает конфигурацию для разработки с полным логированием
   */
  static createDevelopmentConfig(): BrowserTransportManagerConfig {
    const traceConfig = TraceConfigBrowser.loadDefaultConfig();
    const transportConfig = TraceConfigBrowser.createTransportConfig(traceConfig);

    // Настройки для разработки
    if (transportConfig.localStorage) {
      transportConfig.localStorage.maxEvents = 5000;
      transportConfig.localStorage.storageKey = 'filin-trace-events-dev';
    }
    if (transportConfig.fetch) {
      transportConfig.fetch.enabled = false; // Отключено в dev режиме
    }
    if (transportConfig.batchConfig) {
      transportConfig.batchConfig.size = 10; // Маленькие батчи для быстрой отладки
      transportConfig.batchConfig.timeout = 2000;
      transportConfig.batchConfig.maxRetries = 1;
      transportConfig.batchConfig.retryDelay = 500;
    }

    return transportConfig;
  }
}